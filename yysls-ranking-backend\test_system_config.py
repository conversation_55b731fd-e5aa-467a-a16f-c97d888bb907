#!/usr/bin/env python3
"""
系统配置功能测试脚本
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.core.database import SessionLocal
from app.models.system_config import SystemConfig
from app.services.system_config_service import SystemConfigService


def test_system_config():
    """测试系统配置功能"""
    print("🧪 测试系统配置功能")
    print("=" * 40)
    
    db = SessionLocal()
    config_service = SystemConfigService()
    
    try:
        # 1. 测试数据库连接
        print("1. 测试数据库连接...")
        count = db.query(SystemConfig).count()
        print(f"   当前配置数量: {count}")
        
        # 2. 如果没有配置，创建一些测试配置
        if count == 0:
            print("\n2. 创建测试配置...")
            test_configs = [
                {
                    "config_key": "site_name",
                    "config_value": "燕友圈榜单系统",
                    "config_type": "basic",
                    "name": "网站名称",
                    "description": "网站的显示名称",
                    "value_type": "string",
                    "is_active": True,
                    "is_public": True,
                    "group_name": "基础设置",
                    "sort_order": 1
                },
                {
                    "config_key": "enable_registration",
                    "config_value": "true",
                    "config_type": "feature",
                    "name": "开放用户注册",
                    "description": "是否允许新用户注册",
                    "value_type": "boolean",
                    "is_active": True,
                    "is_public": False,
                    "group_name": "功能设置",
                    "sort_order": 10
                }
            ]
            
            for config_data in test_configs:
                config = SystemConfig(**config_data)
                db.add(config)
            
            db.commit()
            print(f"   创建了 {len(test_configs)} 个测试配置")
        
        # 3. 测试获取公开配置
        print("\n3. 测试获取公开配置...")
        public_configs = config_service.get_public_configs(db)
        print(f"   公开配置数量: {len(public_configs)}")
        for key, value in public_configs.items():
            print(f"   - {key}: {value}")
        
        # 4. 测试获取配置分类
        print("\n4. 测试获取配置分类...")
        categories = config_service.get_categories(db)
        print(f"   配置分类: {categories}")
        
        # 5. 测试分页获取配置
        print("\n5. 测试分页获取配置...")
        configs, total = config_service.get_multi_with_total(db, skip=0, limit=10)
        print(f"   配置总数: {total}")
        print(f"   当前页配置数: {len(configs)}")
        for config in configs:
            print(f"   - {config.config_key}: {config.config_value} ({config.config_type})")
        
        print("\n✅ 所有测试通过！")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        db.close()


def main():
    """主函数"""
    print("🚀 燕友圈榜单系统 - 系统配置测试工具")
    print("=" * 50)
    
    success = test_system_config()
    
    if success:
        print("\n🎉 测试完成！系统配置功能正常")
        print("\n💡 接下来可以:")
        print("   1. 启动API服务器测试接口")
        print("   2. 通过管理后台管理配置")
        print("   3. 前端调用公开配置接口")
    else:
        print("\n❌ 测试失败，请检查错误信息")
        sys.exit(1)


if __name__ == "__main__":
    main()
